// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol/grpc/helloworld.proto

package org.ponderers.totoro.infrastructure.rpc.grpc.hello;

public interface HelloReplyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:totoro_infrastructure.HelloReply)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string message = 1;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 1;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();
}
