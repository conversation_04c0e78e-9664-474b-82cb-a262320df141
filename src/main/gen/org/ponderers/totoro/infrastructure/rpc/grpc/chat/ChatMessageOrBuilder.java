// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol/grpc/chat.proto

package org.ponderers.totoro.infrastructure.rpc.grpc.chat;

public interface ChatMessageOrBuilder extends
    // @@protoc_insertion_point(interface_extends:totoro_infrastructure.ChatMessage)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string username = 1;</code>
   * @return The username.
   */
  java.lang.String getUsername();
  /**
   * <code>string username = 1;</code>
   * @return The bytes for username.
   */
  com.google.protobuf.ByteString
      getUsernameBytes();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();
}
