// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol/grpc/helloworld.proto

package org.ponderers.totoro.infrastructure.rpc.grpc.hello;

public final class HelloWorldProto {
  private HelloWorldProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_CloseLogTailerResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_CloseLogTailerResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_HelloRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_HelloRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_HelloReply_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_HelloReply_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_UnaryRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_UnaryRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_UnaryResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_UnaryResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_ClientStreamingRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_ClientStreamingRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_ClientStreamingResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_ClientStreamingResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_ServerStreamingRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_ServerStreamingRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_ServerStreamingResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_ServerStreamingResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_BidiStreamingRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_BidiStreamingRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_BidiStreamingResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_BidiStreamingResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\036protocol/grpc/helloworld.proto\022\025totoro" +
      "_infrastructure\032\033google/protobuf/empty.p" +
      "roto\"(\n\026CloseLogTailerResponse\022\016\n\006result" +
      "\030\001 \001(\t\"\034\n\014HelloRequest\022\014\n\004name\030\001 \001(\t\"\035\n\n" +
      "HelloReply\022\017\n\007message\030\001 \001(\t\"\037\n\014UnaryRequ" +
      "est\022\017\n\007message\030\001 \001(\t\" \n\rUnaryResponse\022\017\n" +
      "\007message\030\001 \001(\t\")\n\026ClientStreamingRequest" +
      "\022\017\n\007message\030\001 \001(\t\"*\n\027ClientStreamingResp" +
      "onse\022\017\n\007message\030\001 \001(\t\")\n\026ServerStreaming" +
      "Request\022\017\n\007message\030\001 \001(\t\"*\n\027ServerStream" +
      "ingResponse\022\017\n\007message\030\001 \001(\t\"\'\n\024BidiStre" +
      "amingRequest\022\017\n\007message\030\001 \001(\t\"(\n\025BidiStr" +
      "eamingResponse\022\017\n\007message\030\001 \001(\t2\324\005\n\006Simp" +
      "le\022T\n\010SayHello\022#.totoro_infrastructure.H" +
      "elloRequest\032!.totoro_infrastructure.Hell" +
      "oReply\"\000\022Y\n\rSayHelloAgain\022#.totoro_infra" +
      "structure.HelloRequest\032!.totoro_infrastr" +
      "ucture.HelloReply\"\000\022W\n\010UnaryRpc\022#.totoro" +
      "_infrastructure.UnaryRequest\032$.totoro_in" +
      "frastructure.UnaryResponse\"\000\022w\n\022ClientSt" +
      "reamingRpc\022-.totoro_infrastructure.Clien" +
      "tStreamingRequest\032..totoro_infrastructur" +
      "e.ClientStreamingResponse\"\000(\001\022w\n\022ServerS" +
      "treamingRpc\022-.totoro_infrastructure.Serv" +
      "erStreamingRequest\032..totoro_infrastructu" +
      "re.ServerStreamingResponse\"\0000\001\022s\n\020BidiSt" +
      "reamingRpc\022+.totoro_infrastructure.BidiS" +
      "treamingRequest\032,.totoro_infrastructure." +
      "BidiStreamingResponse\"\000(\0010\001\022Y\n\016CloseLogT" +
      "ailer\022\026.google.protobuf.Empty\032-.totoro_i" +
      "nfrastructure.CloseLogTailerResponse\"\000B{" +
      "\n2org.ponderers.totoro.infrastructure.rp" +
      "c.grpc.helloB\017HelloWorldProtoP\001Z2org.pon" +
      "derers.totoro.infrastructure.rpc.grpc.he" +
      "llob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.EmptyProto.getDescriptor(),
        });
    internal_static_totoro_infrastructure_CloseLogTailerResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_totoro_infrastructure_CloseLogTailerResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_CloseLogTailerResponse_descriptor,
        new java.lang.String[] { "Result", });
    internal_static_totoro_infrastructure_HelloRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_totoro_infrastructure_HelloRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_HelloRequest_descriptor,
        new java.lang.String[] { "Name", });
    internal_static_totoro_infrastructure_HelloReply_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_totoro_infrastructure_HelloReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_HelloReply_descriptor,
        new java.lang.String[] { "Message", });
    internal_static_totoro_infrastructure_UnaryRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_totoro_infrastructure_UnaryRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_UnaryRequest_descriptor,
        new java.lang.String[] { "Message", });
    internal_static_totoro_infrastructure_UnaryResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_totoro_infrastructure_UnaryResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_UnaryResponse_descriptor,
        new java.lang.String[] { "Message", });
    internal_static_totoro_infrastructure_ClientStreamingRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_totoro_infrastructure_ClientStreamingRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_ClientStreamingRequest_descriptor,
        new java.lang.String[] { "Message", });
    internal_static_totoro_infrastructure_ClientStreamingResponse_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_totoro_infrastructure_ClientStreamingResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_ClientStreamingResponse_descriptor,
        new java.lang.String[] { "Message", });
    internal_static_totoro_infrastructure_ServerStreamingRequest_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_totoro_infrastructure_ServerStreamingRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_ServerStreamingRequest_descriptor,
        new java.lang.String[] { "Message", });
    internal_static_totoro_infrastructure_ServerStreamingResponse_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_totoro_infrastructure_ServerStreamingResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_ServerStreamingResponse_descriptor,
        new java.lang.String[] { "Message", });
    internal_static_totoro_infrastructure_BidiStreamingRequest_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_totoro_infrastructure_BidiStreamingRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_BidiStreamingRequest_descriptor,
        new java.lang.String[] { "Message", });
    internal_static_totoro_infrastructure_BidiStreamingResponse_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_totoro_infrastructure_BidiStreamingResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_BidiStreamingResponse_descriptor,
        new java.lang.String[] { "Message", });
    com.google.protobuf.EmptyProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
