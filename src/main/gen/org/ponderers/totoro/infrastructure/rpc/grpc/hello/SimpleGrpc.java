package org.ponderers.totoro.infrastructure.rpc.grpc.hello;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * The greeting service definition.
 * </pre>
 */
@io.grpc.stub.annotations.GrpcGenerated
public final class SimpleGrpc {

  private SimpleGrpc() {}

  public static final java.lang.String SERVICE_NAME = "totoro_infrastructure.Simple";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> getSayHelloMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SayHello",
      requestType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest.class,
      responseType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> getSayHelloMethod() {
    io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> getSayHelloMethod;
    if ((getSayHelloMethod = SimpleGrpc.getSayHelloMethod) == null) {
      synchronized (SimpleGrpc.class) {
        if ((getSayHelloMethod = SimpleGrpc.getSayHelloMethod) == null) {
          SimpleGrpc.getSayHelloMethod = getSayHelloMethod =
              io.grpc.MethodDescriptor.<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SayHello"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply.getDefaultInstance()))
              .setSchemaDescriptor(new SimpleMethodDescriptorSupplier("SayHello"))
              .build();
        }
      }
    }
    return getSayHelloMethod;
  }

  private static volatile io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> getSayHelloAgainMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SayHelloAgain",
      requestType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest.class,
      responseType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> getSayHelloAgainMethod() {
    io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> getSayHelloAgainMethod;
    if ((getSayHelloAgainMethod = SimpleGrpc.getSayHelloAgainMethod) == null) {
      synchronized (SimpleGrpc.class) {
        if ((getSayHelloAgainMethod = SimpleGrpc.getSayHelloAgainMethod) == null) {
          SimpleGrpc.getSayHelloAgainMethod = getSayHelloAgainMethod =
              io.grpc.MethodDescriptor.<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SayHelloAgain"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply.getDefaultInstance()))
              .setSchemaDescriptor(new SimpleMethodDescriptorSupplier("SayHelloAgain"))
              .build();
        }
      }
    }
    return getSayHelloAgainMethod;
  }

  private static volatile io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse> getUnaryRpcMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UnaryRpc",
      requestType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest.class,
      responseType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse> getUnaryRpcMethod() {
    io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse> getUnaryRpcMethod;
    if ((getUnaryRpcMethod = SimpleGrpc.getUnaryRpcMethod) == null) {
      synchronized (SimpleGrpc.class) {
        if ((getUnaryRpcMethod = SimpleGrpc.getUnaryRpcMethod) == null) {
          SimpleGrpc.getUnaryRpcMethod = getUnaryRpcMethod =
              io.grpc.MethodDescriptor.<org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UnaryRpc"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new SimpleMethodDescriptorSupplier("UnaryRpc"))
              .build();
        }
      }
    }
    return getUnaryRpcMethod;
  }

  private static volatile io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse> getClientStreamingRpcMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "ClientStreamingRpc",
      requestType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest.class,
      responseType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.CLIENT_STREAMING)
  public static io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse> getClientStreamingRpcMethod() {
    io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse> getClientStreamingRpcMethod;
    if ((getClientStreamingRpcMethod = SimpleGrpc.getClientStreamingRpcMethod) == null) {
      synchronized (SimpleGrpc.class) {
        if ((getClientStreamingRpcMethod = SimpleGrpc.getClientStreamingRpcMethod) == null) {
          SimpleGrpc.getClientStreamingRpcMethod = getClientStreamingRpcMethod =
              io.grpc.MethodDescriptor.<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.CLIENT_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ClientStreamingRpc"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse.getDefaultInstance()))
              .setSchemaDescriptor(new SimpleMethodDescriptorSupplier("ClientStreamingRpc"))
              .build();
        }
      }
    }
    return getClientStreamingRpcMethod;
  }

  private static volatile io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse> getServerStreamingRpcMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "ServerStreamingRpc",
      requestType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest.class,
      responseType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
  public static io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse> getServerStreamingRpcMethod() {
    io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse> getServerStreamingRpcMethod;
    if ((getServerStreamingRpcMethod = SimpleGrpc.getServerStreamingRpcMethod) == null) {
      synchronized (SimpleGrpc.class) {
        if ((getServerStreamingRpcMethod = SimpleGrpc.getServerStreamingRpcMethod) == null) {
          SimpleGrpc.getServerStreamingRpcMethod = getServerStreamingRpcMethod =
              io.grpc.MethodDescriptor.<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ServerStreamingRpc"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse.getDefaultInstance()))
              .setSchemaDescriptor(new SimpleMethodDescriptorSupplier("ServerStreamingRpc"))
              .build();
        }
      }
    }
    return getServerStreamingRpcMethod;
  }

  private static volatile io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse> getBidiStreamingRpcMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "BidiStreamingRpc",
      requestType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest.class,
      responseType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
  public static io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse> getBidiStreamingRpcMethod() {
    io.grpc.MethodDescriptor<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse> getBidiStreamingRpcMethod;
    if ((getBidiStreamingRpcMethod = SimpleGrpc.getBidiStreamingRpcMethod) == null) {
      synchronized (SimpleGrpc.class) {
        if ((getBidiStreamingRpcMethod = SimpleGrpc.getBidiStreamingRpcMethod) == null) {
          SimpleGrpc.getBidiStreamingRpcMethod = getBidiStreamingRpcMethod =
              io.grpc.MethodDescriptor.<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest, org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "BidiStreamingRpc"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse.getDefaultInstance()))
              .setSchemaDescriptor(new SimpleMethodDescriptorSupplier("BidiStreamingRpc"))
              .build();
        }
      }
    }
    return getBidiStreamingRpcMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.google.protobuf.Empty,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse> getCloseLogTailerMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "CloseLogTailer",
      requestType = com.google.protobuf.Empty.class,
      responseType = org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.google.protobuf.Empty,
      org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse> getCloseLogTailerMethod() {
    io.grpc.MethodDescriptor<com.google.protobuf.Empty, org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse> getCloseLogTailerMethod;
    if ((getCloseLogTailerMethod = SimpleGrpc.getCloseLogTailerMethod) == null) {
      synchronized (SimpleGrpc.class) {
        if ((getCloseLogTailerMethod = SimpleGrpc.getCloseLogTailerMethod) == null) {
          SimpleGrpc.getCloseLogTailerMethod = getCloseLogTailerMethod =
              io.grpc.MethodDescriptor.<com.google.protobuf.Empty, org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "CloseLogTailer"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.google.protobuf.Empty.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse.getDefaultInstance()))
              .setSchemaDescriptor(new SimpleMethodDescriptorSupplier("CloseLogTailer"))
              .build();
        }
      }
    }
    return getCloseLogTailerMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static SimpleStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<SimpleStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<SimpleStub>() {
        @java.lang.Override
        public SimpleStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new SimpleStub(channel, callOptions);
        }
      };
    return SimpleStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static SimpleBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<SimpleBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<SimpleBlockingStub>() {
        @java.lang.Override
        public SimpleBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new SimpleBlockingStub(channel, callOptions);
        }
      };
    return SimpleBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static SimpleFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<SimpleFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<SimpleFutureStub>() {
        @java.lang.Override
        public SimpleFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new SimpleFutureStub(channel, callOptions);
        }
      };
    return SimpleFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * The greeting service definition.
   * </pre>
   */
  public interface AsyncService {

    /**
     * <pre>
     * Sends a greeting
     * </pre>
     */
    default void sayHello(org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSayHelloMethod(), responseObserver);
    }

    /**
     */
    default void sayHelloAgain(org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSayHelloAgainMethod(), responseObserver);
    }

    /**
     */
    default void unaryRpc(org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUnaryRpcMethod(), responseObserver);
    }

    /**
     */
    default io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest> clientStreamingRpc(
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse> responseObserver) {
      return io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall(getClientStreamingRpcMethod(), responseObserver);
    }

    /**
     */
    default void serverStreamingRpc(org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getServerStreamingRpcMethod(), responseObserver);
    }

    /**
     */
    default io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest> bidiStreamingRpc(
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse> responseObserver) {
      return io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall(getBidiStreamingRpcMethod(), responseObserver);
    }

    /**
     */
    default void closeLogTailer(com.google.protobuf.Empty request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getCloseLogTailerMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service Simple.
   * <pre>
   * The greeting service definition.
   * </pre>
   */
  public static abstract class SimpleImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return SimpleGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service Simple.
   * <pre>
   * The greeting service definition.
   * </pre>
   */
  public static final class SimpleStub
      extends io.grpc.stub.AbstractAsyncStub<SimpleStub> {
    private SimpleStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected SimpleStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new SimpleStub(channel, callOptions);
    }

    /**
     * <pre>
     * Sends a greeting
     * </pre>
     */
    public void sayHello(org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSayHelloMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void sayHelloAgain(org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSayHelloAgainMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void unaryRpc(org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUnaryRpcMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest> clientStreamingRpc(
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse> responseObserver) {
      return io.grpc.stub.ClientCalls.asyncClientStreamingCall(
          getChannel().newCall(getClientStreamingRpcMethod(), getCallOptions()), responseObserver);
    }

    /**
     */
    public void serverStreamingRpc(org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncServerStreamingCall(
          getChannel().newCall(getServerStreamingRpcMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest> bidiStreamingRpc(
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse> responseObserver) {
      return io.grpc.stub.ClientCalls.asyncBidiStreamingCall(
          getChannel().newCall(getBidiStreamingRpcMethod(), getCallOptions()), responseObserver);
    }

    /**
     */
    public void closeLogTailer(com.google.protobuf.Empty request,
        io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getCloseLogTailerMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service Simple.
   * <pre>
   * The greeting service definition.
   * </pre>
   */
  public static final class SimpleBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<SimpleBlockingStub> {
    private SimpleBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected SimpleBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new SimpleBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * Sends a greeting
     * </pre>
     */
    public org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply sayHello(org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSayHelloMethod(), getCallOptions(), request);
    }

    /**
     */
    public org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply sayHelloAgain(org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSayHelloAgainMethod(), getCallOptions(), request);
    }

    /**
     */
    public org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse unaryRpc(org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUnaryRpcMethod(), getCallOptions(), request);
    }

    /**
     */
    public java.util.Iterator<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse> serverStreamingRpc(
        org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest request) {
      return io.grpc.stub.ClientCalls.blockingServerStreamingCall(
          getChannel(), getServerStreamingRpcMethod(), getCallOptions(), request);
    }

    /**
     */
    public org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse closeLogTailer(com.google.protobuf.Empty request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getCloseLogTailerMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service Simple.
   * <pre>
   * The greeting service definition.
   * </pre>
   */
  public static final class SimpleFutureStub
      extends io.grpc.stub.AbstractFutureStub<SimpleFutureStub> {
    private SimpleFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected SimpleFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new SimpleFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * Sends a greeting
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> sayHello(
        org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSayHelloMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply> sayHelloAgain(
        org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSayHelloAgainMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse> unaryRpc(
        org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUnaryRpcMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse> closeLogTailer(
        com.google.protobuf.Empty request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getCloseLogTailerMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SAY_HELLO = 0;
  private static final int METHODID_SAY_HELLO_AGAIN = 1;
  private static final int METHODID_UNARY_RPC = 2;
  private static final int METHODID_SERVER_STREAMING_RPC = 3;
  private static final int METHODID_CLOSE_LOG_TAILER = 4;
  private static final int METHODID_CLIENT_STREAMING_RPC = 5;
  private static final int METHODID_BIDI_STREAMING_RPC = 6;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SAY_HELLO:
          serviceImpl.sayHello((org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest) request,
              (io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply>) responseObserver);
          break;
        case METHODID_SAY_HELLO_AGAIN:
          serviceImpl.sayHelloAgain((org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest) request,
              (io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply>) responseObserver);
          break;
        case METHODID_UNARY_RPC:
          serviceImpl.unaryRpc((org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest) request,
              (io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse>) responseObserver);
          break;
        case METHODID_SERVER_STREAMING_RPC:
          serviceImpl.serverStreamingRpc((org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest) request,
              (io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse>) responseObserver);
          break;
        case METHODID_CLOSE_LOG_TAILER:
          serviceImpl.closeLogTailer((com.google.protobuf.Empty) request,
              (io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_CLIENT_STREAMING_RPC:
          return (io.grpc.stub.StreamObserver<Req>) serviceImpl.clientStreamingRpc(
              (io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse>) responseObserver);
        case METHODID_BIDI_STREAMING_RPC:
          return (io.grpc.stub.StreamObserver<Req>) serviceImpl.bidiStreamingRpc(
              (io.grpc.stub.StreamObserver<org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse>) responseObserver);
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSayHelloMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest,
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply>(
                service, METHODID_SAY_HELLO)))
        .addMethod(
          getSayHelloAgainMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloRequest,
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloReply>(
                service, METHODID_SAY_HELLO_AGAIN)))
        .addMethod(
          getUnaryRpcMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryRequest,
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.UnaryResponse>(
                service, METHODID_UNARY_RPC)))
        .addMethod(
          getClientStreamingRpcMethod(),
          io.grpc.stub.ServerCalls.asyncClientStreamingCall(
            new MethodHandlers<
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingRequest,
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.ClientStreamingResponse>(
                service, METHODID_CLIENT_STREAMING_RPC)))
        .addMethod(
          getServerStreamingRpcMethod(),
          io.grpc.stub.ServerCalls.asyncServerStreamingCall(
            new MethodHandlers<
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingRequest,
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.ServerStreamingResponse>(
                service, METHODID_SERVER_STREAMING_RPC)))
        .addMethod(
          getBidiStreamingRpcMethod(),
          io.grpc.stub.ServerCalls.asyncBidiStreamingCall(
            new MethodHandlers<
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingRequest,
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.BidiStreamingResponse>(
                service, METHODID_BIDI_STREAMING_RPC)))
        .addMethod(
          getCloseLogTailerMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.google.protobuf.Empty,
              org.ponderers.totoro.infrastructure.rpc.grpc.hello.CloseLogTailerResponse>(
                service, METHODID_CLOSE_LOG_TAILER)))
        .build();
  }

  private static abstract class SimpleBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    SimpleBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return org.ponderers.totoro.infrastructure.rpc.grpc.hello.HelloWorldProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("Simple");
    }
  }

  private static final class SimpleFileDescriptorSupplier
      extends SimpleBaseDescriptorSupplier {
    SimpleFileDescriptorSupplier() {}
  }

  private static final class SimpleMethodDescriptorSupplier
      extends SimpleBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    SimpleMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (SimpleGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new SimpleFileDescriptorSupplier())
              .addMethod(getSayHelloMethod())
              .addMethod(getSayHelloAgainMethod())
              .addMethod(getUnaryRpcMethod())
              .addMethod(getClientStreamingRpcMethod())
              .addMethod(getServerStreamingRpcMethod())
              .addMethod(getBidiStreamingRpcMethod())
              .addMethod(getCloseLogTailerMethod())
              .build();
        }
      }
    }
    return result;
  }
}
