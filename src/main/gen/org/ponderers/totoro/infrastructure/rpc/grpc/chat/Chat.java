// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol/grpc/chat.proto

package org.ponderers.totoro.infrastructure.rpc.grpc.chat;

public final class Chat {
  private Chat() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_JoinRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_JoinRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_ChatMessage_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_ChatMessage_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_totoro_infrastructure_Empty_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_totoro_infrastructure_Empty_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030protocol/grpc/chat.proto\022\025totoro_infra" +
      "structure\"\037\n\013JoinRequest\022\020\n\010username\030\001 \001" +
      "(\t\"0\n\013ChatMessage\022\020\n\010username\030\001 \001(\t\022\017\n\007m" +
      "essage\030\002 \001(\t\"\007\n\005Empty2\266\001\n\013ChatService\022V\n" +
      "\010JoinRoom\022\".totoro_infrastructure.JoinRe" +
      "quest\032\".totoro_infrastructure.ChatMessag" +
      "e(\0010\001\022O\n\013SendMessage\022\".totoro_infrastruc" +
      "ture.ChatMessage\032\034.totoro_infrastructure" +
      ".EmptyB5\n1org.ponderers.totoro.infrastru" +
      "cture.rpc.grpc.chatP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_totoro_infrastructure_JoinRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_totoro_infrastructure_JoinRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_JoinRequest_descriptor,
        new java.lang.String[] { "Username", });
    internal_static_totoro_infrastructure_ChatMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_totoro_infrastructure_ChatMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_ChatMessage_descriptor,
        new java.lang.String[] { "Username", "Message", });
    internal_static_totoro_infrastructure_Empty_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_totoro_infrastructure_Empty_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_totoro_infrastructure_Empty_descriptor,
        new java.lang.String[] { });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
