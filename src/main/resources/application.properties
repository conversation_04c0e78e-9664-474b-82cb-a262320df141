# Server Configuration
server.address=0.0.0.0
server.port=8888
server.servlet.context-path=/infrastructure

# Spring Configuration
spring.application.name=totoro-infrastructure
spring.profiles.active=@maven.profiles.active@
spring.output.ansi.enabled=always

# gRPC Configuration
grpc.server.address=*
grpc.server.port=8889

# Logging Configuration
logging.config=classpath:logback-spring.xml
logging.level.root=warn

# Management Configuration
management.health.db.enabled=false
management.endpoints.web.exposure.include=health, metrics, info, loggers
management.endpoint.metrics.enabled=true
management.endpoint.info.enabled=true
management.endpoint.loggers.enabled=true
